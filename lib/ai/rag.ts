import { OpenAI } from "openai";
import { Worker, isMainThread, parentPort, workerData } from "worker_threads";
import { db } from "../db";
import { resources, embeddings, sourceDocuments } from "../db/schema";
import { sql } from "drizzle-orm";
import { Logger } from "../utils/Logger";
import { sendRequestToOpenAI } from "../services/llmService";
interface Metadata {
  docId: string;
  title?: string;
  source?: string;
  contentType?: string;
  [key: string]: any;
}

export interface Document {
  id: string;
  text: string;
  metadata?: Metadata;
}

export interface DocumentChunk {
  id: string;
  text: string;
  embedding: number[];
  norm: number;
  metadata: Metadata;
}

/**
 * This pipeline stores everything in memory for chunking
 * but the *retrieve* method does an entirely DB-based hybrid search.
 */
export class RAGPipeline {
  private openai: OpenAI;
  private embeddingModel: string;

  private chunks: DocumentChunk[] = [];
  private invertedIndex: Map<
      string,
      { df: number; postings: Map<number, number> }
  > = new Map();
  private docLengths: number[] = [];
  private avgDocLength = 0;
  private totalDocs = 0;
  private totalDocLength = 0;
  private embedCache: Map<string, number[]> = new Map();

  private stopWords: Set<string> = new Set([
    "the",
    "and",
    "a",
    "an",
    "is",
    "are",
    "of",
    "to",
    "in",
    "that",
    "for",
    "on",
    "with",
    "as",
    "by",
    "this",
    "it",
    "at",
    "be",
    "from",
  ]);

  constructor() {
    Logger.debug("[RAGPipeline] Initializing RAGPipeline...");
    this.openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
    this.embeddingModel = "text-embedding-3-small";
    Logger.debug(`[RAGPipeline] Using embedding model: ${this.embeddingModel}`);
  }

  /**
   * Basic chunking logic: splits by paragraph/sentence & keeps an overlap approach.
   * You can refine further by token-based chunking for improved accuracy.
   */
  private chunkDocument(doc: Document): string[] {
    Logger.debug(`[chunkDocument] Processing doc ID: ${doc.id}`);
    const text = doc.text;
    const maxTokens = 1024;
    const overlapTokens = 50;

    const paragraphs = text.split(/\n\s*\n/);
    Logger.debug(`[chunkDocument] Found ${paragraphs.length} paragraph(s).`);
    const chunks: string[] = [];
    let currentChunk: string[] = [];
    let currentLength = 0;

    const pushChunk = (chunkWords: string[]) => {
      const chunkText = chunkWords.join(" ");
      if (chunkText.trim().length > 0) {
        chunks.push(chunkText);
        Logger.debug(
            `[chunkDocument] Pushed chunk (words=${
                chunkWords.length
            }): "${chunkText.slice(0, 80)}..."`
        );
      }
    };

    for (const para of paragraphs) {
      const sentences = para.split(/(?<=[.?!])\s+/);
      for (const sentence of sentences) {
        const words = sentence.split(/\s+/);
        if (currentLength + words.length > maxTokens && currentLength > 0) {
          const overlapCount = Math.min(overlapTokens, currentLength);
          const overlapWords = currentChunk.slice(
              currentChunk.length - overlapCount
          );
          pushChunk(currentChunk);
          currentChunk = overlapWords.slice();
          currentLength = overlapWords.length;
        }
        currentChunk.push(...words);
        currentLength += words.length;

        if (currentLength >= maxTokens) {
          const overlapCount = Math.min(overlapTokens, currentLength);
          const overlapWords = currentChunk.slice(
              currentChunk.length - overlapCount
          );
          pushChunk(currentChunk);
          currentChunk = overlapWords.slice();
          currentLength = overlapWords.length;
        }
      }
    }
    if (currentLength > 0) {
      pushChunk(currentChunk);
    }
    Logger.debug(`[chunkDocument] Total chunks created: ${chunks.length}`);
    return chunks;
  }

  /**
   * Embed a piece of text with caching to avoid repeated API calls.
   */
  async embedText(text: string): Promise<number[]> {
    Logger.debug(`[embedText] Embedding text of length ${text.length}`);
    if (this.embedCache.has(text)) {
      Logger.debug("[embedText] Cache hit!");
      return this.embedCache.get(text)!;
    }
    try {
      const response = await this.openai.embeddings.create({
        model: this.embeddingModel,
        input: text,
      });
      const vector = response.data[0].embedding as number[];
      this.embedCache.set(text, vector);
      return vector;
    } catch (error) {
      Logger.error("[embedText] Error generating embedding:", error);
      throw error;
    }
  }

  /**
   * Build an in-memory BM25 index for local usage (if you still want to do local retrieval).
   * Even if retrieve() eventually uses the DB, we can preserve the original logic.
   */
  private indexChunk(docIndex: number, text: string): void {
    Logger.debug(`[indexChunk] Indexing chunk #${docIndex}`);
    const terms = text.toLowerCase().match(/\w+/g) || [];
    const filtered = terms.filter((t) => !this.stopWords.has(t));
    const docLength = filtered.length;
    this.docLengths[docIndex] = docLength;
    this.totalDocs++;
    this.totalDocLength += docLength;

    const termFreq: Map<string, number> = new Map();
    for (const t of filtered) {
      termFreq.set(t, (termFreq.get(t) || 0) + 1);
    }
    termFreq.forEach((freq, term) => {
      let entry = this.invertedIndex.get(term);
      if (!entry) {
        entry = { df: 0, postings: new Map() };
        this.invertedIndex.set(term, entry);
      }
      entry.df += 1;
      entry.postings.set(docIndex, freq);
    });
  }

  /**
   * initialize() remains mostly the same: chunk, embed, store in memory + DB.
   */
  async initialize(docs: Document[]): Promise<void> {
    Logger.debug("[initialize] Starting RAGPipeline initialization...");
    this.chunks = [];
    this.invertedIndex.clear();
    this.docLengths = [];
    this.totalDocs = 0;
    this.totalDocLength = 0;

    const tasks: Promise<void>[] = [];

    for (const doc of docs) {
      Logger.debug(`[initialize] Document ID: ${doc.id}`);
      const metadata = {
        docId: doc.id,
        title: doc.metadata?.title,
        source: doc.metadata?.source,
        contentType: doc.metadata?.contentType,
      };
      const docChunks = this.chunkDocument(doc);
      for (let i = 0; i < docChunks.length; i++) {
        const chunkText = docChunks[i];
        const chunkId = `${doc.id}_chunk_${i}`;
        const promise = this.embedText(chunkText)
            .then(async (vector) => {
              const norm = Math.sqrt(vector.reduce((s, x) => s + x * x, 0));
              const chunk: DocumentChunk = {
                id: chunkId,
                text: chunkText,
                embedding: vector,
                norm,
                metadata,
              };
              const docIndex = this.chunks.length;
              this.chunks.push(chunk);

              // Build BM25 in memory (if you still care about local usage)
              this.indexChunk(docIndex, chunkText);

              // Also store in DB
              try {
                await db.transaction(async (tx) => {
                  const [resourceRow] = await tx
                      .insert(resources)
                      .values({
                        content: chunkText,
                        sourceDocumentId: doc.id,
                        // any other columns you have, like chatId
                      })
                      .returning();

                  await tx.insert(embeddings).values({
                    resourceId: resourceRow.id,
                    content: chunkText,
                    embedding: vector,
                  });
                });
              } catch (dbErr) {
                Logger.error(
                    `[initialize] Error storing chunk ${chunkId} in DB:`,
                    dbErr
                );
              }
            })
            .catch((err) => {
              Logger.error(`[initialize] Error embedding chunk ${chunkId}:`, err);
            });
        tasks.push(promise);
      }
    }

    await Promise.all(tasks);
    this.avgDocLength =
        this.totalDocs > 0 ? this.totalDocLength / this.totalDocs : 0;
    Logger.debug(
        `[initialize] Completed. In-memory chunk count: ${this.chunks.length}, totalDocs=${this.totalDocs}`
    );
  }

  /**
   *
   * Steps:
   * 1) embed the query
   * 2) create a text-search query (like plainto_tsquery or websearch_to_tsquery)
   * 3) do a SELECT from resources + embeddings
   *    - compute text ranking via ts_rank (approx BM25)
   *    - compute vector similarity via cosine_similarity
   *    - combine them as alpha * ts_rank + (1 - alpha) * vector_score
   * 4) order by that combined score DESC
   * 5) limit to topK
   *
   * This requires:
   *  - a "vector" column in embeddings (pgvector)
   *  - a "tsvector" column in resources or on-the-fly with to_tsvector(...)
   *  - We do an inline to_tsvector(...) in this example. In production, you might store it in a dedicated column for performance.
   */
  async retrieve(query: string, topK: number, chatId?: string): Promise<any[]> {
    Logger.debug(
        `[retrieve] Running DB-based retrieval for "${query}" (topK=${topK})`
    );

    // 1) Get the query embedding
    const queryVector = await this.embedText(query);

    // 2) Convert user query into a TS query.
    //    For instance, websearch_to_tsquery('english', 'this is a test')
    //    or plainto_tsquery('english', 'this is a test')
    //    We'll do a simple approach:
    const textSearchQuery = sql`websearch_to_tsquery('english', ${query})`;
    // or: sql`plainto_tsquery('english', ${query})`;

    // 3) The "alpha" parameter for combining TS rank and vector similarity
    const alpha = 0.5;

    // 4) Build the selection with a combined score
    //
    //  => text_score:  ts_rank_cd(to_tsvector('english', resources.content), textSearchQuery)
    //  => vec_score:   cosine_similarity(embeddings.embedding, queryVec)
    //  => combined:    alpha * text_score + (1-alpha) * vec_score
    //
    // If you have a "chatId" filter in sourceDocuments, add it to your WHERE clause below.
    const results = await db
        .select({
          resourceId: resources.id,
          content: resources.content,
          similarity: sql`(
      ${alpha}::float8 * ts_rank_cd(to_tsvector('english', ${
              resources.content
          }), ${textSearchQuery})
      + (1 - ${alpha}::float8) * cosine_similarity(${
              embeddings.embedding
          }, ${sql.raw(`'[${queryVector.join(",")}]'::vector`)})
    )`.as("hybrid_score"),
        })
        .from(embeddings)
        .innerJoin(resources, sql`${embeddings.resourceId} = ${resources.id}`)
        .innerJoin(
            sourceDocuments,
            sql`${resources.sourceDocumentId} = ${sourceDocuments.id}`
        )
        .where(
            chatId
                ? sql`${sourceDocuments.chatId} = ${chatId}
            AND (
              to_tsvector('english', ${resources.content}) @@ ${textSearchQuery}
              OR cosine_similarity(${embeddings.embedding}, ${sql.raw(
                    `'[${queryVector.join(",")}]'::vector`
                )}) > 0
            )`
                : sql`(
            to_tsvector('english', ${resources.content}) @@ ${textSearchQuery}
            OR cosine_similarity(${embeddings.embedding}, ${sql.raw(
                    `'[${queryVector.join(",")}]'::vector`
                )}) > 0
          )`
        )
        .orderBy(sql`hybrid_score DESC`)
        .limit(topK);

    Logger.debug(
        `[retrieve] Found ${results.length} chunk(s) from the DB for query "${query}".`
    );
    return results;
  }

  /**
   * This method remains if you want to add new documents incrementally.
   * We store them in memory (BM25, local vectors) but also the DB.
   */
  async addDocument(doc: Document): Promise<void> {
    Logger.debug(`[addDocument] Adding doc ID: ${doc.id}`);
    const metadata = {
      docId: doc.id,
      title: doc.metadata?.title,
      source: doc.metadata?.source,
      contentType: doc.metadata?.contentType,
    };
    const docChunks = this.chunkDocument(doc);

    for (let i = 0; i < docChunks.length; i++) {
      const chunkText = docChunks[i];
      const chunkId = `${doc.id}_chunk_${i}`;
      try {
        const vector = await this.embedText(chunkText);
        const norm = Math.sqrt(vector.reduce((sum, x) => sum + x * x, 0));
        const chunk: DocumentChunk = {
          id: chunkId,
          text: chunkText,
          embedding: vector,
          norm,
          metadata,
        };
        const docIndex = this.chunks.length;
        this.chunks.push(chunk);

        // BM25 index in memory
        this.indexChunk(docIndex, chunkText);

        // also store in DB
        await db.transaction(async (tx) => {
          const [resourceRow] = await tx
              .insert(resources)
              .values({
                content: chunkText,
                sourceDocumentId: doc.id,
              })
              .returning();
          await tx.insert(embeddings).values({
            resourceId: resourceRow.id,
            content: chunkText,
            embedding: vector,
          });
        });
      } catch (err) {
        Logger.error(`[addDocument] Error processing chunk ${chunkId}:`, err);
      }
    }

    // update average doc length, etc.
    this.avgDocLength =
        this.totalDocs > 0 ? this.totalDocLength / this.totalDocs : 0;
    Logger.debug(
        `[addDocument] Document ID ${doc.id} added with ${docChunks.length} chunk(s).`
    );
  }
}

// -----------------------------------------------------------------
// -----------------------------------------------------------------
if (!isMainThread && parentPort && workerData) {
  Logger.debug(
      "[Worker] Worker thread started for partial similarity computation."
  );
  const { queryVec, embeddings, norms } = workerData;
  const queryNorm = Math.sqrt(
      queryVec.reduce((sum: number, x: number) => sum + x * x, 0)
  );
  const scores: number[] = embeddings.map((emb: number[], i: number) => {
    let dot = 0;
    for (let j = 0; j < emb.length; j++) {
      dot += queryVec[j] * emb[j];
    }
    return dot / (queryNorm * norms[i]);
  });
  Logger.debug(
      "[Worker] Computed similarity scores, sending back to main thread."
  );
  parentPort.postMessage(scores);
  process.exit(0);
}

/**
 * Calls an LLM to refine (enhance) the user's raw prompt to make it more
 * effective for retrieval in our RAG pipeline. Adjust the system instruction
 * and model call as needed (OpenAI, Anthropic, etc.).
 */
export async function refineUserPromptForRAG(
    userPrompt: string,
    conversationContext: string
): Promise<string> {
  // Example system instruction for OpenAI (can adapt for Anthropic or PaLM)
  const systemInstruction = `
You are a Query Refiner AI specialized in legal document retrieval in a Retrieval-Augmented Generation (RAG) pipeline. Your task is to transform a user's raw query into a refined legal search query that maximizes both relevance and recall. Follow these guidelines:

1. **Include Synonyms and Related Terms:** Identify key legal terms and enrich them with appropriate synonyms or related legal concepts. For instance, if the query includes "attorney," also consider "lawyer" or "counsel."

2. **Expand Abbreviations/Acronyms:** Replace abbreviations with their full forms (e.g., "CFR" → "Code of Federal Regulations") to ensure that the query matches documents that may use either form.

3. **Emphasize Key Legal Terms:** Focus on important names, case titles, statutes, and legal concepts by removing generic filler words. Ensure that the refined query highlights the core legal issue.

4. **Disambiguate Ambiguous Terms:** Clarify any terms that may have multiple meanings by adding context-specific qualifiers (e.g., "trust (legal)" or "contract law consideration") to avoid confusion.

5. **Incorporate Relevant Conversation Context:** Seamlessly integrate any critical details from previous dialogue (such as jurisdiction, specific cases, or legal topics) so the query stands alone with all necessary context.

6. **Keep it Concise and Targeted:** Produce a single, well-formed sentence or phrase that captures the refined legal query. Eliminate unnecessary verbosity while retaining essential details.

**Input:** A user's raw query along with the conversation context provided below.
**Output:** A refined legal search query optimized for retrieving relevant documents.

## Conversation Context
${conversationContext}
`;

  // Call your desired LLM. Here, we use a non-streaming OpenAI request
  // but you could replace it with `sendRequestToAnthropic` or `sendRequestToGooglePalm`.
  const refinedQuery = await sendRequestToOpenAI(userPrompt, systemInstruction);

  // If the LLM returns nothing, fall back to the original prompt
  return refinedQuery || userPrompt;
}
