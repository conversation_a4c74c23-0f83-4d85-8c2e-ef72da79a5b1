import { OpenAI } from "openai";
import { Worker, isMainThread, parentPort, workerData } from "worker_threads";
import { db } from "../db";
import { resources, embeddings, sourceDocuments } from "../db/schema";
import { sql } from "drizzle-orm";
import { Logger } from "../utils/Logger";
import { sendRequestToOpenAI } from "../services/llmService";
interface Metadata {
  docId: string;
  title?: string;
  source?: string;
  contentType?: string;
  [key: string]: any;
}

export interface Document {
  id: string;
  text: string;
  metadata?: Metadata;
}

export interface DocumentChunk {
  id: string;
  text: string;
  embedding: number[];
  norm: number;
  metadata: Metadata;
}

/**
 * This pipeline stores everything in memory for chunking
 * but the *retrieve* method does an entirely DB-based hybrid search.
 */
export class RAGPipeline {
  private openai: OpenAI;
  private embeddingModel: string;

  private chunks: DocumentChunk[] = [];
  private invertedIndex: Map<
      string,
      { df: number; postings: Map<number, number> }
  > = new Map();
  private docLengths: number[] = [];
  private avgDocLength = 0;
  private totalDocs = 0;
  private totalDocLength = 0;
  private embedCache: Map<string, number[]> = new Map();

  private stopWords: Set<string> = new Set([
    "the",
    "and",
    "a",
    "an",
    "is",
    "are",
    "of",
    "to",
    "in",
    "that",
    "for",
    "on",
    "with",
    "as",
    "by",
    "this",
    "it",
    "at",
    "be",
    "from",
  ]);

  constructor() {
    Logger.debug("[RAGPipeline] Initializing RAGPipeline...");
    this.openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
    this.embeddingModel = "text-embedding-3-small";
    Logger.debug(`[RAGPipeline] Using embedding model: ${this.embeddingModel}`);
  }

  /**
   * Basic chunking logic: splits by paragraph/sentence & keeps an overlap approach.
   * You can refine further by token-based chunking for improved accuracy.
   */
  private chunkDocument(doc: Document): string[] {
    Logger.debug(`[chunkDocument] Processing doc ID: ${doc.id}`);
    const text = doc.text;
    const maxTokens = 1024;
    const overlapTokens = 50;

    const paragraphs = text.split(/\n\s*\n/);
    Logger.debug(`[chunkDocument] Found ${paragraphs.length} paragraph(s).`);
    const chunks: string[] = [];
    let currentChunk: string[] = [];
    let currentLength = 0;

    const pushChunk = (chunkWords: string[]) => {
      const chunkText = chunkWords.join(" ");
      if (chunkText.trim().length > 0) {
        chunks.push(chunkText);
        Logger.debug(
            `[chunkDocument] Pushed chunk (words=${
                chunkWords.length
            }): "${chunkText.slice(0, 80)}..."`
        );
      }
    };

    for (const para of paragraphs) {
      const sentences = para.split(/(?<=[.?!])\s+/);
      for (const sentence of sentences) {
        const words = sentence.split(/\s+/);
        if (currentLength + words.length > maxTokens && currentLength > 0) {
          const overlapCount = Math.min(overlapTokens, currentLength);
          const overlapWords = currentChunk.slice(
              currentChunk.length - overlapCount
          );
          pushChunk(currentChunk);
          currentChunk = overlapWords.slice();
          currentLength = overlapWords.length;
        }
        currentChunk.push(...words);
        currentLength += words.length;

        if (currentLength >= maxTokens) {
          const overlapCount = Math.min(overlapTokens, currentLength);
          const overlapWords = currentChunk.slice(
              currentChunk.length - overlapCount
          );
          pushChunk(currentChunk);
          currentChunk = overlapWords.slice();
          currentLength = overlapWords.length;
        }
      }
    }
    if (currentLength > 0) {
      pushChunk(currentChunk);
    }
    Logger.debug(`[chunkDocument] Total chunks created: ${chunks.length}`);
    return chunks;
  }

  /**
   * Embed a piece of text with caching to avoid repeated API calls.
   */
  async embedText(text: string): Promise<number[]> {
    Logger.debug(`[embedText] Embedding text of length ${text.length}`);
    if (this.embedCache.has(text)) {
      Logger.debug("[embedText] Cache hit!");
      return this.embedCache.get(text)!;
    }
    try {
      const response = await this.openai.embeddings.create({
        model: this.embeddingModel,
        input: text,
      });
      const vector = response.data[0].embedding as number[];
      this.embedCache.set(text, vector);
      return vector;
    } catch (error) {
      Logger.error("[embedText] Error generating embedding:", error);
      throw error;
    }
  }

  /**
   * Build an in-memory BM25 index for local usage (if you still want to do local retrieval).
   * Even if retrieve() eventually uses the DB, we can preserve the original logic.
   */
  private indexChunk(docIndex: number, text: string): void {
    Logger.debug(`[indexChunk] Indexing chunk #${docIndex}`);
    const terms = text.toLowerCase().match(/\w+/g) || [];
    const filtered = terms.filter((t) => !this.stopWords.has(t));
    const docLength = filtered.length;
    this.docLengths[docIndex] = docLength;
    this.totalDocs++;
    this.totalDocLength += docLength;

    const termFreq: Map<string, number> = new Map();
    for (const t of filtered) {
      termFreq.set(t, (termFreq.get(t) || 0) + 1);
    }
    for (const [term, freq] of termFreq) {
      let entry = this.invertedIndex.get(term);
      if (!entry) {
        entry = { df: 0, postings: new Map() };
        this.invertedIndex.set(term, entry);
      }
      entry.df += 1;
      entry.postings.set(docIndex, freq);
    }
  }

  /**
   * initialize() remains mostly the same: chunk, embed, store in memory + DB.
   */
  async initialize(docs: Document[]): Promise<void> {
    Logger.debug("[initialize] Starting RAGPipeline initialization...");
    this.chunks = [];
    this.invertedIndex.clear();
    this.docLengths = [];
    this.totalDocs = 0;
    this.totalDocLength = 0;

    const tasks: Promise<void>[] = [];

    for (const doc of docs) {
      Logger.debug(`[initialize] Document ID: ${doc.id}`);
      const metadata = {
        docId: doc.id,
        title: doc.metadata?.title,
        source: doc.metadata?.source,
        contentType: doc.metadata?.contentType,
      };
      const docChunks = this.chunkDocument(doc);
      for (let i = 0; i < docChunks.length; i++) {
        const chunkText = docChunks[i];
        const chunkId = `${doc.id}_chunk_${i}`;
        const promise = this.embedText(chunkText)
            .then(async (vector) => {
              const norm = Math.sqrt(vector.reduce((s, x) => s + x * x, 0));
              const chunk: DocumentChunk = {
                id: chunkId,
                text: chunkText,
                embedding: vector,
                norm,
                metadata,
              };
              const docIndex = this.chunks.length;
              this.chunks.push(chunk);

              // Build BM25 in memory (if you still care about local usage)
              this.indexChunk(docIndex, chunkText);

              // Also store in DB
              try {
                await db.transaction(async (tx) => {
                  const [resourceRow] = await tx
                      .insert(resources)
                      .values({
                        content: chunkText,
                        sourceDocumentId: doc.id,
                        // any other columns you have, like chatId
                      })
                      .returning();

                  await tx.insert(embeddings).values({
                    resourceId: resourceRow.id,
                    content: chunkText,
                    embedding: vector,
                  });
                });
              } catch (dbErr) {
                Logger.error(
                    `[initialize] Error storing chunk ${chunkId} in DB:`,
                    dbErr
                );
              }
            })
            .catch((err) => {
              Logger.error(`[initialize] Error embedding chunk ${chunkId}:`, err);
            });
        tasks.push(promise);
      }
    }

    await Promise.all(tasks);
    this.avgDocLength =
        this.totalDocs > 0 ? this.totalDocLength / this.totalDocs : 0;
    Logger.debug(
        `[initialize] Completed. In-memory chunk count: ${this.chunks.length}, totalDocs=${this.totalDocs}`
    );
  }
